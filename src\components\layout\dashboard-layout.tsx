
import React, { useState } from 'react';
import { NavLink, useNavigate, Outlet } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  LayoutGrid,
  ClipboardCheck,
  AlertTriangle,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronRight,
  ClipboardList,
  Users,
  ShieldAlert,
  FileWarning,
  FolderOpen
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogTrigger
} from '@/components/ui/dialog';


// Mobile Navigation Component
const MobileNav: React.FC<{
  navItems: Array<{
    to: string;
    icon: React.ReactNode;
    label: string;
    requiredRole: string;
    end?: boolean;
  }>;
  hasPermission: (role: string) => boolean;
  onLogout: () => void;
}> = ({ navItems, hasPermission, onLogout }) => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <div className="grid gap-4 py-4">
          <nav className="space-y-2">
            {navItems.map((item) => (
              hasPermission(item.requiredRole) && (
                <NavLink
                  key={item.to}
                  to={item.to}
                  end={item.end}
                  onClick={() => setOpen(false)}
                  className={({ isActive }) => cn(
                    "flex items-center px-3 py-2 rounded-md hover:bg-muted-foreground/10 transition-colors duration-150",
                    isActive ? "bg-muted-foreground/10 text-primary font-medium" : "text-muted-foreground"
                  )}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  <span className="ml-3">{item.label}</span>
                </NavLink>
              )
            ))}
            <Button
              variant="ghost"
              onClick={() => {
                onLogout();
                setOpen(false);
              }}
              className="w-full justify-start text-muted-foreground hover:text-foreground"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Logout
            </Button>
          </nav>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export function DashboardLayout() {
  const { currentUser, logout, hasPermission } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out"
      });
      navigate('/login');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout",
        variant: "destructive"
      });
    }
  };

  const navItems = [
    {
      to: '/dashboard',
      icon: <LayoutGrid className="h-5 w-5" />,
      label: 'Dashboard',
      requiredRole: 'viewer',
      end: true
    },
    {
      to: '/prerequisite-programs',
      icon: <ClipboardList className="h-5 w-5" />,
      label: 'Prerequisite Programs',
      requiredRole: 'viewer'
    },
    {
      to: '/food-safety-culture',
      icon: <Users className="h-5 w-5" />,
      label: 'Food Safety Culture',
      requiredRole: 'viewer'
    },
    {
      to: '/allergen-management',
      icon: <ShieldAlert className="h-5 w-5" />,
      label: 'Allergen Management',
      requiredRole: 'viewer'
    },
    {
      to: '/capa',
      icon: <FileWarning className="h-5 w-5" />,
      label: 'CAPA Management',
      requiredRole: 'viewer'
    },
    {
      to: '/document-management',
      icon: <FolderOpen className="h-5 w-5" />,
      label: 'Document Management',
      requiredRole: 'viewer'
    },
    {
      to: '/hazard-analysis',
      icon: <AlertTriangle className="h-5 w-5" />,
      label: 'Hazard Analysis',
      requiredRole: 'viewer'
    },
    {
      to: '/ccp-management',
      icon: <ClipboardCheck className="h-5 w-5" />,
      label: 'CCP Management',
      requiredRole: 'viewer'
    },
    {
      to: '/plan-generator',
      icon: <FileText className="h-5 w-5" />,
      label: 'Plan Generator',
      requiredRole: 'viewer'
    },
    {
      to: '/settings',
      icon: <Settings className="h-5 w-5" />,
      label: 'Settings',
      requiredRole: 'qa'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
        {/* Header */}
        <header className="bg-white border-b shadow-sm z-10">
          <div className="mx-auto px-4 flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
                className="hidden md:flex"
              >
                {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
              <MobileNav
                navItems={navItems}
                hasPermission={hasPermission}
                onLogout={handleLogout}
              />
              <h1 className="text-xl font-bold text-primary">HACCP Manager</h1>
            </div>

            <div className="flex items-center gap-4">
              {currentUser && (
                <>
                  <div className="text-sm text-right">
                    <p className="font-medium">{currentUser.name}</p>
                    <p className="text-muted-foreground capitalize">{currentUser.role}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleLogout}
                    aria-label="Logout"
                    className="hidden md:flex"
                  >
                    <LogOut className="h-5 w-5" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </header>

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside
          className={cn(
            "bg-muted border-r transition-all duration-200 ease-in-out z-10",
            sidebarOpen ? "w-64" : "w-16"
          )}
        >
          <nav className="p-2 space-y-1">
            {navItems.map((item) => (
              hasPermission(item.requiredRole as any) && (
                <NavLink
                  key={item.to}
                  to={item.to}
                  end={item.end}
                  className={({ isActive }) => cn(
                    "flex items-center px-3 py-2 rounded-md hover:bg-muted-foreground/10 transition-colors duration-150",
                    isActive ? "bg-muted-foreground/10 text-primary font-medium" : "text-muted-foreground",
                    sidebarOpen ? "justify-start" : "justify-center"
                  )}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  {sidebarOpen && <span className="ml-3">{item.label}</span>}
                </NavLink>
              )
            ))}
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 overflow-auto p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default DashboardLayout;
